/**
 * The name of the configuration file entered in the production environment
 */

import dayjs from 'dayjs';
import { getEnvConfig } from './utils';

const envConfig = getEnvConfig('VITE_');
const vitePublicPath = envConfig.VITE_PUBLIC_PATH;
const basicOutputDir = `dist-学工-人脸识别-前端包-${dayjs(new Date()).format('YYYYMMDD')}-001`;
const finalOutputDir = vitePublicPath && vitePublicPath !== '/' ? `${basicOutputDir}${vitePublicPath}` : basicOutputDir;

export const GLOB_CONFIG_FILE_NAME = '_app.config.js';
export const OUTPUT_DIR = finalOutputDir;
