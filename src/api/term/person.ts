/*
 * @Description:
 * @Autor: panmy
 * @Date: 2025-04-22 16:05:06
 * @LastEditors: panmy
 * @LastEditTime: 2025-07-01 16:05:25
 */
import { defHttp } from '@/utils/http/axios';

enum Api {
  Prefix = '/api/term/person',
}

export function getList(data = {}) {
  return defHttp.get({ url: `${Api.Prefix}/getList`, data });
}

// 获取所有人员列表（不分页）
export function getPersonAllList(data = {}) {
  return defHttp.get({ url: `${Api.Prefix}/getList`, data });
}

export function remove(id = {}) {
  return defHttp.delete({ url: `${Api.Prefix}/remove/${id}` });
}
export function batchRemove(data = {}) {
  return defHttp.delete({ url: `${Api.Prefix}/batchRemove`, data });
}
export function getDetailInfo(data = {}) {
  return defHttp.get({ url: `${Api.Prefix}/${data.id}` });
}
export function edit(data = {}) {
  return defHttp.put({ url: `${Api.Prefix}/edit/${data.id}`, data });
}
export function save(data = {}) {
  return defHttp.post({ url: `${Api.Prefix}/save`, data });
}

export function sysData(data = {}) {
  return defHttp.post({ url: `${Api.Prefix}/sysData`, data });
}

// 导入数据
export function importBigData() {
  return defHttp.get({ url: `${Api.Prefix}/importBigData` });
}

export function sendPerson(data = {}) {
  return defHttp.post({ url: `${Api.Prefix}/sendPerson`, data });
}
