/*
 * @Description:
 * @Autor: panmy
 * @Date: 2025-04-27 18:26:28
 * @LastEditors: panmy
 * @LastEditTime: 2025-04-27 18:28:45
 */
import { defHttp } from '@/utils/http/axios';

enum Api {
  Prefix = '/api/term/batch/send/log',
}

// 查询详情
export function getDetail(id: string) {
  return defHttp.get({ url: `${Api.Prefix}/${id}` });
}

// 编辑
export function edit(id: string, data = {}) {
  return defHttp.put({ url: `${Api.Prefix}/edit/${id}`, data });
}

// 保存
export function save(data = {}) {
  return defHttp.post({ url: `${Api.Prefix}/save`, data });
}

// 批量保存
export function batchSave(data = {}) {
  return defHttp.post({ url: `${Api.Prefix}/batchSave`, data });
}

// 获取列表（不分页）
export function getListAll() {
  return defHttp.get({ url: `${Api.Prefix}/listAll` });
}

// 获取列表（分页）
export function getList(data = {}) {
  return defHttp.get({ url: `${Api.Prefix}/getList`, data });
}

// 导入数据
export function importBigData() {
  return defHttp.get({ url: `${Api.Prefix}/importBigData` });
}

// 批量删除
export function batchRemove(data = {}) {
  return defHttp.delete({ url: `${Api.Prefix}/batchRemove`, data });
}

// 删除
export function remove(id: string) {
  return defHttp.delete({ url: `${Api.Prefix}/remove/${id}` });
}
