/*
 * @Description:批次和设备绑定关系
 * @Autor: panmy
 * @Date: 2025-04-28 09:36:52
 * @LastEditors: panmy
 * @LastEditTime: 2025-07-07 21:55:34
 */
import { defHttp } from '@/utils/http/axios';

enum Api {
  Prefix = '/api/term/face/batch/terminal',
}

// 查询详情
export function getDetail(id: string) {
  return defHttp.get({ url: `${Api.Prefix}/${id}` });
}

// 编辑
export function edit(id: string, data = {}) {
  return defHttp.put({ url: `${Api.Prefix}/edit/${id}`, data });
}

// 保存
export function save(data = {}) {
  return defHttp.post({ url: `${Api.Prefix}/save`, data });
}

// 批量保存
export function batchSave(data = []) {
  return defHttp.post({ url: `${Api.Prefix}/batchSave`, data });
}
// 批量保存
export function batchSaveByBatchId(batchId, data = []) {
  return defHttp.post({ url: `${Api.Prefix}/batchSave/${batchId}`, data });
}

// 批量保存
export function batchEdit(data = []) {
  return defHttp.put({ url: `${Api.Prefix}/batchEdit`, data });
}

// 获取列表（不分页）
export function getListAll(data) {
  return defHttp.get({ url: `${Api.Prefix}/listAll`, data });
}

// 获取列表（分页）
export function getList(data = {}) {
  return defHttp.get({ url: `${Api.Prefix}/getList`, data });
}

// 导入数据
export function importBigData() {
  return defHttp.get({ url: `${Api.Prefix}/importBigData` });
}

// 批量删除
export function batchRemove(data = []) {
  return defHttp.delete({ url: `${Api.Prefix}/batchRemove`, data });
}

// 删除
export function remove(id: string) {
  return defHttp.delete({ url: `${Api.Prefix}/remove/${id}` });
}

export function getAuthorizationTerminal(data) {
  return defHttp.get({ url: `${Api.Prefix}/${data.id}` });
}
