/*
 * @Description:
 * @Autor: panmy
 * @Date: 2025-05-22 09:28:28
 * @LastEditors: panmy
 * @LastEditTime: 2025-05-22 09:35:12
 */
import { defHttp } from '@/utils/http/axios';

enum Api {
  Prefix = '/api/term/query',
}

// 总人数核验情况统计
export function getTotalPersonStatistics(data) {
  return defHttp.get({ url: `${Api.Prefix}/getTotalPersonStatistics/${data.id}`, data });
}

// 核验时间端统计情况
export function getTimeStatistics(data) {
  return defHttp.get({ url: `${Api.Prefix}/getTimeStatistics/${data.id}`, data });
}

// 组织核验人数情况统计
export function getOrganizationStatistics(data) {
  return defHttp.get({ url: `${Api.Prefix}/getOrganizationStatistics/${data.id}`, data });
}

// 设备核验人数情况统计
export function getDeviceStatistics(data) {
  return defHttp.get({ url: `${Api.Prefix}/getDeviceStatistics/${data.id}`, data });
}
