/*
 * @Description:
 * @Autor: panmy
 * @Date: 2025-04-28 09:36:52
 * @LastEditors: panmy
 * @LastEditTime: 2025-07-16 09:19:39
 */

import { defHttp } from '@/utils/http/axios';

enum Api {
  Prefix = '/api/term/face/batch',
}

// 查询详情
export function getDetail(id: string) {
  return defHttp.get({ url: `${Api.Prefix}/${id}` });
}

// 编辑
export function edit(data = {}) {
  return defHttp.put({ url: `${Api.Prefix}/edit/${data.id}`, data });
}

// 保存
export function save(data = {}) {
  return defHttp.post({ url: `${Api.Prefix}/save`, data });
}

// 批量保存
export function batchSave(data = {}) {
  return defHttp.post({ url: `${Api.Prefix}/batchSave`, data });
}

// 获取列表（不分页）
export function getListAll(data) {
  return defHttp.get({ url: `${Api.Prefix}/listAll`, data });
}

// 获取列表（分页）
export function getList(data = {}) {
  return defHttp.get({ url: `${Api.Prefix}/getList`, data });
}

// 导入数据
export function importBigData() {
  return defHttp.get({ url: `${Api.Prefix}/importBigData` });
}

// 批量删除
export function batchRemove(data = {}) {
  return defHttp.delete({ url: `${Api.Prefix}/batchRemove`, data });
}

// 删除
export function remove(id: string) {
  return defHttp.delete({ url: `${Api.Prefix}/remove/${id}` });
}

// 下发参数
export function sendParams(data = {}) {
  return defHttp.post({ url: `${Api.Prefix}/sendParams`, data });
}

// 批量下发参数和人员
export function sendParamsAndPerson(data = {}) {
  return defHttp.post({ url: `${Api.Prefix}/sendParamsAndPerson`, data });
}

export function statistics(data) {
  return defHttp.get({ url: `${Api.Prefix}/statistics`, data });
}

export function updateParamsAndPerson(data) {
  return defHttp.post({ url: `${Api.Prefix}/updateParamsAndPerson`, data });
}
export function sendPersons(data) {
  return defHttp.post({ url: `${Api.Prefix}/sendPersons`, data });
}
