/*
 * @Description:
 * @Autor: panmy
 * @Date: 2025-04-22 16:05:06
 * @LastEditors: panmy
 * @LastEditTime: 2025-06-26 10:18:52
 */
import { defHttp } from '@/utils/http/axios';

enum Api {
  Prefix = '/api/term/terminal',
}

export function getList(data = {}) {
  return defHttp.get({ url: `${Api.Prefix}/getList`, data });
}
// 获取列表（不分页）
export function getListAll() {
  return defHttp.get({ url: `${Api.Prefix}/listAll` });
}

export function batchRemove(data = {}) {
  return defHttp.delete({ url: `${Api.Prefix}/batchRemove`, data });
}
export function getTerminal(data = {}) {
  return defHttp.get({ url: `${Api.Prefix}/${data.id}` });
}
export function getTerminalByCode(code) {
  return defHttp.get({ url: `${Api.Prefix}/byCodeInfo/${code}` });
}

export function edit(data = {}) {
  return defHttp.put({ url: `${Api.Prefix}/edit/${data.id}`, data });
}
export function save(data = {}) {
  return defHttp.post({ url: `${Api.Prefix}/save`, data });
}
export function clearBatch(data = {}) {
  return defHttp.delete({ url: `${Api.Prefix}/removeData/${data.type}`, data });
}
export function clearBatch2(data = {}) {
  return defHttp.post({ url: `/api/term/terminal/removeData`, data });
}
