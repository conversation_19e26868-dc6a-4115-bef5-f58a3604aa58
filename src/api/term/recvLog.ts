/*
 * @Description: 下发日志管理
 * @Autor: panmy
 * @Date: 2025-04-28 09:36:52
 * @LastEditors: panmy
 * @LastEditTime: 2025-06-16 12:38:00
 */
import { defHttp } from '@/utils/http/axios';
enum Api {
  Prefix = '/api/term/recv/log',
}

// 查询详情
export function getDetail(id: string) {
  return defHttp.get({ url: `${Api.Prefix}/${id}` });
}

// 编辑
export function edit(id: string, data = {}) {
  return defHttp.put({ url: `${Api.Prefix}/edit/${id}`, data });
}

// 保存
export function save(data = {}) {
  return defHttp.post({ url: `${Api.Prefix}/save`, data });
}

// 批量保存
export function batchSave(data = []) {
  return defHttp.post({ url: `${Api.Prefix}/batchSave`, data });
}

// 获取列表（不分页）
export function getListAll(data) {
  return defHttp.get({ url: `${Api.Prefix}/listAll`, data });
}

// 获取列表（分页）
export function getList(data = {}) {
  return defHttp.get({ url: `${Api.Prefix}/getList`, data });
}

// 导入数据
export function importBigData() {
  return defHttp.get({ url: `${Api.Prefix}/importBigData` });
}

// 批量删除
export function batchRemove(data = {}) {
  return defHttp.delete({ url: `${Api.Prefix}/batchRemove`, data });
}

// 删除
export function remove(id: string) {
  return defHttp.delete({ url: `${Api.Prefix}/remove/${id}` });
}

export function getDownloadUrl(params, data) {
  return defHttp.post(
    {
      url: `${Api.Prefix}/export`,
      params,
      data,
      responseType: 'blob',
    },
    {
      isTransformResponse: false, // 不对请求数据进行转换处理
      isReturnNativeResponse: true, // 返回原生响应对象，包含headers
    },
  );
}

export function exportConfig(data) {
  return defHttp.get({ url: `${Api.Prefix}/exportConfig`, data });
}
