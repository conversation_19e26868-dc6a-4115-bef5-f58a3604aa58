/*
 * @Description: 学习类
 * @Autor: panmy
 * @Date: 2025-03-19 14:26:53
 * @LastEditors: panmy
 * @LastEditTime: 2025-03-19 15:38:27
 */

import { defHttp } from '@/utils/http/axios';

enum Api {
  Prefix = '/api/schedule/BaseCardInfo',
}

// 获取院系,专业,班级 , 院系-专业 , 专业-班级, 院系-专业-班级 级联数据
export function getShoolStructureTypeList(data) {
  return defHttp.get({ url: `/api/public-base/cascade/cascade`, data });
}
