/*
 * @Description:
 * @Autor: panmy
 * @Date: 2025-04-14 11:04:41
 * @LastEditors: panmy
 * @LastEditTime: 2025-04-17 13:07:41
 */
import { defHttp } from '@/utils/http/axios';

// 新增生物特征信息
export function addSwtz(params: any) {
  return defHttp.post({ url: '/xg/swtz/add', data: params });
}

// 删除生物特征库信息
export function deleteSwtz(params: any) {
  return defHttp.post({ url: '/xg/swtz/delete', data: params });
}

// 生物特征详细信息查询
export function getSwtz(id: string) {
  return defHttp.get({ url: `/xg/swtz/get/${id}` });
}

// 批量导入生物特征信息
export function importBatchInfo(params: any) {
  return defHttp.post({ url: '/xg/swtz/importBatch', data: params });
}

// 批量导入生物特征照片
export function importBatchPhoto(params: any) {
  return defHttp.post({ url: '/xg/swtz/importPhoto', data: params });
}

// 生物特征查询列表分页
export function querySwtz(params: any) {
  return defHttp.get({ url: `/xg/swtz/page`, data: params });
}

// 修改生物特征信息
export function updateSwtz(params: any) {
  return defHttp.post({ url: '/xg/swtz/update', data: params });
}

// 批量下发人员
export function issueSwtz(params: any) {
  return defHttp.post({ url: '/xg/swtz/issueBatch', data: params });
}

// 生物特征图表
export function getBarSwtz(params: any) {
  return defHttp.get({ url: `/xg/swtz/barGraph`, data: params });
}

// 生物特征 -- 导入教职工
export function importJzg(params: any) {
  return defHttp.post({ url: '/xg/swtz/importFaculty', data: params });
}

// 生物特征 -- 导入学生
export function importStu(params: any) {
  return defHttp.post({ url: '/xg/swtz/importStudent', data: params });
}

// 生物特征 -- 批量下发失败人员
export function issueFairPeople() {
  return defHttp.post({ url: '/xg/swtz/issueBatchs' });
}

// 生物特征 -- 全部下发人员
export function issueAllPeople(params: any) {
  return defHttp.post({ url: '/xg/swtz/issueBatches', data: params });
}
