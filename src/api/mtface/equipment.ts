import { defHttp } from '@/utils/http/axios';

// 终端型号分页查询
export function queryModelPage(params) {
  return defHttp.get({ url: '/equipment/model/getModelPage', params });
}

// 新增终端型号
export function insertModel(params) {
  return defHttp.post({ url: '/equipment/model/insertModel', data: params });
}

// 修改终端型号
export function updateModel(params) {
  return defHttp.post({ url: '/equipment/model/updateModel', data: params });
}

// 删除终端型号
export function deleteModel(params) {
  return defHttp.post({ url: '/equipment/model/deleteModel', data: params });
}

// 获取型号集合
export function getModelList(params) {
  return defHttp.get({ url: '/equipment/model/getModelList', params });
}

// 根据边缘节点序列号获取库编号集合
export function getLibByDeviceSn(params) {
  return defHttp.get({ url: '/equipment/lib/getLibByDeviceSn', params });
}

// 终端设备分页查询
export function queryEquipmentPage(params) {
  return defHttp.get({ url: '/equipment/getEquipmentPage', params });
}

// 批量清除数据
export function clearBatch(params) {
  return defHttp.post({ url: '/equipment/clearBatchData', data: params });
}

// 下发参数
export function queryIssueParam(params) {
  return defHttp.post({ url: '/equipment/issueParam', data: params });
}

// 更新设备人员数量
export function updatePersonNum(params) {
  return defHttp.post({ url: '/equipment/updateDevicePersonNum', data: params });
}

// 新增终端设备
export function insertEquipment(params) {
  return defHttp.post({ url: '/equipment/insertEquipment', data: params });
}

// 修改终端设备
export function updateEquipment(params) {
  return defHttp.post({ url: '/equipment/updateEquipment', data: params });
}

// 批量删除终端设备
export function deleteEquipment(params) {
  return defHttp.post({ url: '/equipment/deleteBatchDevice', data: params });
}

// 更新设备使用状态
export function updateUseStatus(params) {
  return defHttp.post({ url: '/equipment/updateUseStatus', data: params });
}

// 检测设备网络状态
export function checkDeviceNet(params) {
  return defHttp.post({ url: '/equipment/checkDeviceNet', data: params });
}

// 根据序列号获取设备信息
export function getDeviceBySn(params) {
  return defHttp.get({ url: '/equipment/getDeviceBySn', params });
}

// 根据id获取设备信息
export function getDeviceById(params) {
  return defHttp.get({ url: '/equipment/getDeviceInfoById', params });
}

// 无感知终端分页查询
export function queryUnawarePage(params) {
  return defHttp.get({ url: '/equipment/unaware/getPage', params });
}

// 新增无感知设备
export function insertUnaware(params) {
  return defHttp.post({ url: '/equipment/unaware/insert', data: params });
}

// 修改无感知设备
export function updateUnaware(params) {
  return defHttp.post({ url: '/equipment/unaware/update', data: params });
}

// 删除无感知设备
export function deleteUnaware(params) {
  return defHttp.post({ url: '/equipment/unaware/delete', data: params });
}

// 获取设备集合 -- 归属终端
export function getEqumentList(params) {
  return defHttp.get({ url: '/equipment/getEquipmentList', params });
}

// 参数模板分页查询
export function queryParamTmpl(params) {
  return defHttp.get({ url: '/equipment/paramTmpl/getPage', params });
}

// 新增设备参数模板
export function insertParamTmpl(params) {
  return defHttp.post({ url: '/equipment/paramTmpl/insert', data: params });
}

// 修改设备参数模板
export function updateParamTmpl(params) {
  return defHttp.post({ url: '/equipment/paramTmpl/update', data: params });
}

// 删除设备参数模板
export function deleteParamTmpl(params) {
  return defHttp.post({ url: '/equipment/paramTmpl/deleteBatch', data: params });
}

// 根据id获取参数模板
export function getTmplById(params) {
  return defHttp.get({ url: '/equipment/paramTmpl/getTmplById', params });
}

// 获取参数模板集合
export function getParamTmplList(params) {
  return defHttp.get({ url: '/equipment/paramTmpl/getList', params });
}

// 批量设置设备参数
export function setBatchParam(params) {
  return defHttp.post({ url: '/equipment/paramTmpl/setBatchParam', data: params });
}

// 任务项人员分页查询
export function queryPersonPage(params) {
  return defHttp.get({ url: '/equipment/taskItem/getPersonnelPage', params });
}

// 任务项分页查询
export function querySubTaskPage(params) {
  return defHttp.get({ url: '/equipment/taskItem/getSubtaskPage', params });
}

// 执行终端任务项
export function reRunTask(params) {
  return defHttp.post({ url: '/equipment/taskItem/performSubtask', data: params });
}

// 获取日志详情
export function queryLogDetail(params) {
  return defHttp.get({ url: '/equipment/log/getLogDetail', params });
}

// 终端日志分页查询
export function queryLogPage(params) {
  return defHttp.get({ url: '/equipment/log/getLogPage', params });
}

// 识别日志分页查询
export function queryRecordPage(params) {
  return defHttp.get({ url: '/equipment/record/getRecordPage', params });
}

// 识别日志分页查询
export function studentRecordPage(params) {
  return defHttp.get({ url: '/equipment/record/getRecordByModulePage', params });
}

// 根据日志获取识别记录
export function queryRecordByLog(params) {
  return defHttp.get({ url: '/equipment/record/getRecordDetail', params });
}

// 获取识别日志饼图数据
export function getRecordPie() {
  return defHttp.get({ url: '/equipment/record/getRecordForPie' });
}

// 设备下发人员分页查询
export function queryPersonnelPage(params) {
  return defHttp.get({ url: '/equipment/personnel/getPersonBydeviceSnPage', params });
}

// 设备下发人员 -- 批量删除
export function removePerson(params) {
  return defHttp.post({ url: '/equipment/personnel/removeBatch', data: params });
}

// 应用模块数据
export function getModuleList() {
  return defHttp.get({ url: '/sys/xgYg/getList' });
}

// 根据id查询模块权限
export function getSelectByTtdId(params) {
  return defHttp.get({ url: '/system/sysUserYg/selectByEqpId', params });
}

// 修改用户模块权限
export function getSelectUpdate(params) {
  return defHttp.post({ url: '/system/sysUserYg/update', data: params });
}
