<!--
 * @Description: 应用终端
 * @Autor: panmy
 * @Date: 2025-04-27 10:06:08
 * @LastEditors: panmy
 * @LastEditTime: 2025-07-17 16:02:00
-->
<template>
  <BasicModal
    v-bind="$attrs"
    class="transfer-modal member-modal"
    @register="registerModal"
    :title="`应用终端——${state.parametersName}`"
    :width="1000"
    :bodyStyle="{
      height: '630px',
    }">
    <template #footer>
      <div class="flex justify-between">
        <div></div>
        <div>
          <a-button type="primary" :loading="state.btnLoading" @click="handleSubmit(0)">保存</a-button>
          <a-button type="primary" :loading="state.btnLoading" @click="handleSubmit(1)">保存并下发</a-button>
          <a-button type="error" @click="closeModal()">关闭</a-button>
        </div>
      </div>
    </template>
    <BasicTable @register="registerTable">
      <template #bodyCell="{ column, record, index }">
        <template v-if="column.key === 'online'">
          <a-tag :color="getOnlineStatusColor(record.online)">{{ record.online ? '在线' : '离线' }}</a-tag>
        </template>

        <template v-if="column.key === 'status'">
          <a-tag :color="record.status == '1' || record.status == 1 ? 'green' : 'red'">{{
            record.status == '1' || record.status == 1 ? '启用' : record.status == '0' || record.status == 0 ? '停用' : '--'
          }}</a-tag>
        </template>
      </template>
    </BasicTable>
  </BasicModal>
</template>
<script lang="ts" setup>
  import { ref } from 'vue';
  import { BasicModal, useModalInner } from '@/components/Modal';
  import { BasicTable, useTable } from '@/components/Table';
  import { useMessage } from '@/hooks/web/useMessage';
  import { defineEmits, nextTick, reactive, computed } from 'vue';
  import * as parametersApi from '@/api/term/parameters';
  import * as terminalApi from '@/api/term/terminal';

  const { createConfirm, createMessage } = useMessage();

  const emit = defineEmits(['register', 'reload']);
  const state = reactive({
    btnLoading: false,
    parametersId: '',
    parametersName: '',
  });
  const [registerModal, { closeModal, changeLoading, changeOkLoading }] = useModalInner(async data => {
    state.parametersId = data.id || '';
    state.parametersName = data.name || '';
    clearSelectedRowKeys();
    reload();
    state.btnLoading = false;
  });

  // 动态表单配置
  const columns = [
    {
      title: '终端编号',
      dataIndex: 'code',
      align: 'center',
      width: 120,
      resizable: true,
      ellipsis: true,
      fixed: 'left',
    },
    {
      title: '终端序列号',
      dataIndex: 'terminalSn',
      align: 'center',
      width: 100,
      fixed: 'left',
      customRender: ({ record }) => {
        return record?.terminalSn || record?.devcSn || record?.sn;
      },
    },
    {
      title: '终端名称',
      dataIndex: 'name',
      align: 'center',
      width: 120,
      resizable: true,
      ellipsis: true,
    },

    {
      title: '终端类型',
      dataIndex: 'devcType',
      align: 'center',
      width: 120,
      resizable: true,
      ellipsis: true,
    },
    {
      title: '终端型号',
      dataIndex: 'deviType',
      align: 'center',
      width: 120,
      resizable: true,
      ellipsis: true,
    },
    {
      title: '归属应用',
      dataIndex: 'moduleName',
      align: 'center',
      width: 170,
      resizable: true,
      ellipsis: true,
    },
    {
      title: '终端状态',
      dataIndex: 'online',
      align: 'center',
      width: 90,
      resizable: true,
      ellipsis: true,

      fixed: 'right',
    },
    {
      title: '当前参数模板',
      dataIndex: 'paramTemplateName',
      align: 'center',
      width: 120,
      resizable: true,
      ellipsis: true,
      fixed: 'right',
    },
  ];
  const [registerTable, { reload, getForm, getSelectRowKeys, getFetchParams, getSelectRows, clearSelectedRowKeys }] = useTable({
    api: terminalApi.getList,
    columns,
    useSearchForm: true,
    immediate: false,
    rowSelection: {
      type: 'checkbox',
    },
    formConfig: {
      schemas: [
        {
          field: 'keyword',
          label: '关键字',
          component: 'Input',
          colProps: {
            span: 12, // 设置为12，占用更多宽度（默认是6）
          },
          componentProps: {
            placeholder: '输入终端编号、名称关键字搜索',
            submitOnPressEnter: true,
          },
        },
      ],
    },
    scroll: {
      y: 400,
    },
  });

  function getOnlineStatusColor(status) {
    return status == '1' || status === true ? 'green' : 'red';
  }

  async function handleSubmit(batchType) {
    const rowKeys = getSelectRows();
    if (rowKeys.length === 0) {
      createMessage.warning('请至少选择一个终端');
      return;
    }
    const sns = rowKeys.map(item => item?.sn);
    const params = getFetchParams();
    state.btnLoading = true;
    changeLoading(true);
    try {
      const res = await parametersApi.batchSaveParas({ id: state.parametersId, terminalSns: sns, batchType });
      createMessage.success(res.msg);
      emit('reload');
      closeModal();
    } catch (error) {
      console.error('提交失败:', error);
    } finally {
      changeLoading(false);
      state.btnLoading = false;
    }
  }
</script>
