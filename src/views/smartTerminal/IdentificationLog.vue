<!--
 * @Description: 识别日志 
 * @Autor: panmy
 * @Date: 2025-04-14 10:33:54
 * @LastEditors: panmy
 * @LastEditTime: 2025-07-27 20:33:12
-->
<template>
  <div class="mtcn-content-wrapper">
    <div class="mtcn-content-wrapper-center">
      <div class="mtcn-content-wrapper-content bg-white h-full">
        <BasicTable @register="registerTable2">
          <template #tableTitle>
            <a-button type="primary" @click="handleExport" preIcon="icon-ym icon-ym-btn-upload button-preIcon">导出</a-button>
          </template>
          <template #bodyCell="{ column, record, index }">
            <template v-if="column.dataIndex === 'status'">
              <a-tag :color="HCStatusMap[record.fstatus || HCStatusEnum.ERROR].color">
                {{ HCStatusMap[record.fstatus || HCStatusEnum.ERROR].text }}
              </a-tag>
            </template>
            <template v-if="['facePhoto'].includes(column.dataIndex)">
              <a-image
                :width="30"
                :height="30"
                style="object-fit: cover"
                :src="getFacePhoto(record)"
                :preview.stop="{
                  src: getFacePhoto(record),
                }" />
            </template>
            <template v-if="column.dataIndex === 'action'">
              <TableAction :actions="getTableActions(record, index)" :dropDownActions="[]" />
            </template>
          </template>
        </BasicTable>
      </div>
    </div>
    <ExportModal @register="registerExportModal" @download="handleDownload" />
    <TerminalLogDetailForm @register="registerTerminalLogDetailForm" @reload="reload2" />
    <TerminalLogDetailList @register="registerTerminalLogDetailList" />
  </div>
</template>

<script lang="ts" setup>
  import * as termBatchApi from '@/api/term/batch';
  import * as recvLogApi from '@/api/term/recvLog';
  import * as sendLogApi from '@/api/term/sendLog';
  import * as terminalApi from '@/api/term/terminal';
  import { SuperQueryModal } from '@/components/CommonModal';
  import ExportModal from '@/components/CommonModal/src/ExportModal.vue';
  import { useModal } from '@/components/Modal';
  import { usePopup } from '@/components/Popup';
  import { BasicTable, TableAction, useTable } from '@/components/Table';
  import { useMessage } from '@/hooks/web/useMessage';
  import { fetchAndNameDownload } from '@/utils/file/download';
  import TerminalLogDetailForm from '@/views/smartTerminal/Log/TerminalLogDetailForm.vue';
  import TerminalLogDetailList from '@/views/smartTerminal/Log/TerminalLogDetailList.vue';
  import { FilterOutlined } from '@ant-design/icons-vue';
  import { Descriptions, DescriptionsItem, Popover, Tag } from 'ant-design-vue';
  import { onMounted, reactive, ref } from 'vue';
  import dayjs from 'dayjs';
  import noImg from '@/assets/images/noImg.png';
  import {
    SendStatusMap,
    SendStatusEnum,
    TerminalLogTypeOptions,
    TerminalLogType,
    HCStatusEnum,
    HCStatusMap,
    inftCodeTypeOptions,
    inftCodeType,
    HYStatusMap,
    HYStatusEnum,
    RECOTYPES,
  } from '@/enums/otherEnum';
  import TermManageDetail from '@/views/smartTerminal/TermManage/Detail.vue';

  const activeKey = ref('2');
  const { createMessage, createConfirm } = useMessage();
  const [registerTermManageDetail, { openPopup: openTermManageDetail }] = usePopup();

  const columns2 = [
    {
      title: '终端名称',
      dataIndex: 'terminalName',
      ellipsis: true,
      customRender: ({ record, text }) => {
        return record?.termTerminalVo?.name;
      },
    },
    {
      title: '终端编号',
      dataIndex: 'terminalCode',
      align: 'center',

      ellipsis: true,
      customRender: ({ record, text }) => {
        return text || record?.termTerminalVo?.code;
      },
    },
    {
      title: '终端序列号',
      dataIndex: 'terminalSn',

      align: 'center',
      ellipsis: true,
      customRender: ({ record }) => {
        return record?.terminalSn || record?.devcSn || record?.sn;
      },
    },
    {
      title: '终端IP',
      dataIndex: 'thIp',

      align: 'center',
      ellipsis: true,
      customRender: ({ record, text }) => {
        return text || record?.termTerminalVo?.thIp;
      },
    },
    {
      title: '回传编码',
      dataIndex: 'intfCode',
      align: 'center',
      width: 150,
      ellipsis: true,
      customRender: ({ record }) => {
        return record.intfCode; // ? inftCodeType[record.intfCode] : '';
      },
    },
    {
      title: '回传名称',
      dataIndex: 'intfName',

      align: 'center',
      ellipsis: true,
    },
    {
      title: '回传照片',
      dataIndex: 'facePhoto',
      with: 80,
      align: 'center',
      ellipsis: true,
    },
    {
      title: '是否是陌生人',
      dataIndex: 'facePhoto1',
      with: 90,
      align: 'center',
      ellipsis: true,
      customRender: ({ record }) => {
        return record.exceptionType == 1 ? '是' : '否';
      },
    },
    {
      title: '处理状态',
      dataIndex: 'status',

      align: 'center',
    },
    {
      title: '处理说明',
      dataIndex: 'descText',
      ellipsis: true,
      customRender: ({ record }) => {
        return record.descText;
      },
    },
    {
      title: '处理时间',
      dataIndex: 'creatorTime',
      width: 160,
      format: 'date|YYYY-MM-DD HH:mm:ss',
      sorter: true,
      sortFn: (a, b) => {
        return new Date(a.creatorTime) - new Date(b.creatorTime);
      },
    },
  ];
  const getFacePhoto = record => {
    const message = record.message;
    if (message) {
      try {
        const data = JSON.parse(message);
        const data2 = JSON.parse(data.data);
        if (!data2.facePhoto) return noImg;
        return 'data:image/png;base64,' + data2.facePhoto;
      } catch (error) {
        return noImg;
      }
    }
    return '';
  };

  const [
    registerTable2,
    {
      reload: reload2,
      getForm: getForm2,
      getSelectRowKeys: getSelectRowKeys2,
      clearSelectedRowKeys: clearSelectedRowKeys2,
      getFetchParams: getFetchParams2,
      setLoading: setLoading2,
    },
  ] = useTable({
    api: recvLogApi.getList,
    columns: columns2,
    useSearchForm: true,
    immediate: true,
    rowSelection: {
      type: 'checkbox',
    },
    clickToRowSelect: false,
    canResize: true,
    sortOpts: {
      defaultSortFn: sortInfo => {
        return {
          sortField: sortInfo.field,
          sortOrder: sortInfo.order === 'ascend' ? 'asc' : 'desc',
        };
      },
    },
    formConfig: {
      // 配置高级搜索
      superQueryConfig: {
        enabled: false,
        getColumnOptions: () => getColumnOptions(),
      },
      schemas: [
        {
          field: 'keyword',
          label: '关键字',
          component: 'Input',
          componentProps: {
            placeholder: '输入终端IP关键字搜索',
            submitOnPressEnter: true,
          },
        },

        {
          field: 'sn',
          label: '终端名称',
          component: 'Select',
          componentProps: {
            showSearch: true,
            placeholder: '全部',
            allowClear: false,
          },
        },
        {
          field: 'exceptionType',
          label: '是否是陌生人',
          component: 'Select',
          defaultValue: '',
          componentProps: {
            placeholder: '全部',
            options: [
              { id: '', fullName: '全部' },
              { id: '1', fullName: '是' },
              { id: '0', fullName: '否' },
            ],
          },
        },
      ],
    },
    beforeFetch: params => {
      params.intfCode = 'VERIFICATION_RESULT';
    },
    actionColumn: {
      width: 120,
      title: '操作',
      align: 'center',
      dataIndex: 'action',
    },
  });

  const [registerTerminalLogDetailForm, { openPopup: openTerminalLogDetailForm }] = usePopup();
  const [registerTerminalLogDetailList, { openPopup: openTerminalLogDetailList }] = usePopup();

  const handleTabs = key => {
    activeKey.value = key;
    if (key === '1') {
      setTimeout(() => {
        reload1({ page: 1 });
      });
    } else {
      setTimeout(() => {
        getForm2().updateSchema({
          field: 'sn',
          componentProps: {
            options: terminalList.value,
            fieldNames: { label: 'name', value: 'sn' },
          },
        });
        reload2({ page: 1 });
      });
    }
  };
  function getTableActions(record) {
    return [
      {
        ifShow:
          activeKey.value === '1' && !['SENDDELETEPERSON', 'SENDDELETELOG', 'SENDDELETEALL'].includes(record?.sendType ? record.sendType.toUpperCase() : ''),
        label: '详情',
        onClick: () => getDetail(record),
      },
      {
        ifShow: activeKey.value === '2',
        label: '详情',
        onClick: () => openTerminalLogDetailForm(true, { ...record, type: 1, isSend: 0 }),
      },
    ];
  }

  function getDetail(record) {
    if (['SENDBATCH', 'SENDPARAMS'].includes(record?.sendType ? record?.sendType.toUpperCase() : '')) {
      const params = {
        type: record?.sendType ? record.sendType.toUpperCase() : '',
        logData: record,
      };
      openTermManageDetail(true, params);
    } else {
      openTerminalLogDetailList(true, record);
    }
  }

  function handleBatchRemove() {
    const ids = getSelectRowKeys1();
    if (!ids.length) return createMessage.warning('请选择至少一个模板');
    createConfirm({
      iconType: 'warning',
      title: '提示',
      content: '您确定要删除这些模板吗?',
      onOk: () => {
        deleteTemplate(ids).then(() => {
          createMessage.success('删除成功');
          clearSelectedRowKeys1();
          reload();
        });
      },
    });
  }

  const deviceParams = ref({});
  const getDeviceByInfo = async (visible, terminalCode) => {
    if (!visible) return;
    setLoading1(true);
    const { data } = await terminalApi.getTerminalByCode(terminalCode);
    deviceParams.value = data;
    setLoading1(false);
  };

  const [registerExportModal, { openModal: openExportModal, closeModal }] = useModal();
  const exportParams = ref({
    field: null,
    exportCol: [],
  });

  async function handleExport() {
    // 获取查询参数
    const listQuery = activeKey.value == 1 ? getFetchParams1() : getFetchParams2();
    openExportModal(true, { listQuery, exportType: activeKey.value == 1 ? 'TermBatchSendLogExport' : 'TermRecvLogExport' });
  }

  async function handleDownload(data) {
    const listQuery = activeKey.value == 1 ? getFetchParams1() : getFetchParams2();
    // 构建查询参数对象，不包含json
    const queryParams = {
      ...listQuery,
      ...data,
    };

    try {
      const api = activeKey.value == 1 ? sendLogApi : recvLogApi;
      const response = await api.getDownloadUrl({}, queryParams);

      fetchAndNameDownload(response);
      setTimeout(() => {
        closeModal();
      }, 2 * 1000);
    } catch (error) {
      createMessage.error('文件下载失败');
      console.error('下载文件出错:', error);
      closeModal();
    }
  }
  async function getLoadOptions() {
    const sendBatchIdRes = await termBatchApi.getListAll({});
    getForm1().updateSchema({ field: 'sendBatchId', componentProps: { options: sendBatchIdRes?.data || [], fieldNames: { label: 'batchName', value: 'id' } } });
  }

  //#region 高级查询
  const searchInfo = reactive({ superQueryJson: undefined });
  function handleSuperQuery(queryJson) {
    searchInfo.superQueryJson = queryJson;
    if (activeKey.value === '1') {
      reload1({ page: 1 });
    } else {
      reload2({ page: 1 });
    }
  }
  function getColumnOptions() {
    return [
      {
        fullName: '终端名称',
        id: 'terminalName',
        __config__: {
          jnpfKey: 'input',
        },
      },
      {
        fullName: '终端编号',
        id: 'terminalCode',
        __config__: {
          jnpfKey: 'input',
        },
      },
      {
        fullName: '终端IP',
        id: 'terminalIp',
        __config__: {
          jnpfKey: 'input',
        },
      },
      {
        fullName: '下发类型',
        id: 'sendType',
        __config__: {
          jnpfKey: 'select',
        },
        options: TerminalLogTypeOptions,
        props: { label: 'fullName', value: 'id' },
      },
      {
        fullName: '下发状态',
        id: 'sendStatus',
        __config__: {
          jnpfKey: 'select',
        },
        options: [
          { id: '1', fullName: '成功' },
          { id: '0', fullName: '失败' },
          { id: '2', fullName: '错误' },
        ],
        props: { label: 'fullName', value: 'id' },
      },
    ];
  }
  //#endregion

  const terminalList = ref([]);
  onMounted(async () => {
    reload2();

    const { data } = await terminalApi.getListAll({});
    terminalList.value = data;
    getForm2().updateSchema({
      field: 'sn',
      componentProps: {
        options: data,
        fieldNames: { label: 'name', value: 'sn' },
      },
    });
  });
</script>
