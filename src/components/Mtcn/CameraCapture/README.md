# MtcnCameraCapture 拍照组件

一个完全自包含的电脑端拍照组件，支持摄像头拍照、图片裁剪和自动上传功能。

## 设计理念

这是一个**完全自包含**的组件，内部处理所有逻辑：

- 拍照功能
- 图片裁剪
- 自动上传
- 复用 MtcnUploadImgSingle 组件显示已上传图片

**无需外部处理任何事件**，直接使用 `v-model` 绑定即可。拍照完成后自动切换到 MtcnUploadImgSingle 组件显示。

## 功能特性

- ✅ 电脑端摄像头拍照
- ✅ 多种摄像头兼容性处理（前置/后置/默认）
- ✅ 图片实时裁剪和预览
- ✅ 自动上传到服务器
- ✅ 图片预览和删除功能
- ✅ 完全自包含，无需外部事件处理
- ✅ 与 MtcnUploadImgSingle 相同的 API

## 使用方法

### 基础用法

```vue
<template>
  <MtcnCameraCapture v-model:value="imageUrl" buttonText="拍照" tipText="现场拍照" :enableCrop="true" />
</template>

<script setup>
  import { ref } from 'vue';

  const imageUrl = ref('');
</script>
```

### 在表单中使用（指定裁剪尺寸）

```vue
<template>
  <a-form>
    <a-form-item label="现场照片" required>
      <MtcnCameraCapture v-model:value="formData.photo" buttonText="拍照" tipText="请拍摄现场照片" :enableCrop="true" :cropWidth="90" :cropHeight="115" />
    </a-form-item>
  </a-form>
</template>

<script setup>
  import { reactive } from 'vue';

  const formData = reactive({
    photo: '',
  });
</script>
```

### 自定义裁剪配置

```vue
<template>
  <MtcnCameraCapture v-model:value="imageUrl" buttonText="拍摄头像" tipText="请拍摄头像照片" :enableCrop="true" :cropperOptions="avatarCropperOptions" />
</template>

<script setup>
  import { ref } from 'vue';

  const imageUrl = ref('');

  // 圆形头像裁剪配置
  const avatarCropperOptions = {
    aspectRatio: 1, // 1:1 比例
    viewMode: 1,
    dragMode: 'move',
    autoCropArea: 0.8,
    restore: false,
    guides: true,
    center: true,
    highlight: false,
    cropBoxMovable: true,
    cropBoxResizable: false, // 固定裁剪框大小
    toggleDragModeOnDblclick: false,
  };
</script>
```

## Props 参数

| 参数           | 类型    | 默认值     | 说明                     |
| -------------- | ------- | ---------- | ------------------------ |
| value          | string  | ''         | 图片 URL，支持 v-model   |
| buttonText     | string  | '拍照'     | 拍照按钮文本             |
| tipText        | string  | ''         | 提示文本                 |
| enableCrop     | boolean | true       | 是否启用裁剪功能         |
| cropWidth      | number  | 0          | 裁剪宽度(0=使用默认比例) |
| cropHeight     | number  | 0          | 裁剪高度(0=使用默认比例) |
| cropperOptions | object  | -          | 裁剪器配置选项           |
| imageQuality   | number  | 0.8        | 图片质量(0-1)            |
| maxWidth       | number  | 1920       | 最大宽度                 |
| maxHeight      | number  | 1080       | 最大高度                 |
| type           | string  | 'annexpic' | 上传类型                 |
| disabled       | boolean | false      | 是否禁用                 |

## Events 事件

| 事件名       | 参数            | 说明       |
| ------------ | --------------- | ---------- |
| update:value | (value: string) | 值更新事件 |
| change       | (value: string) | 值改变事件 |

## 默认裁剪配置

```javascript
{
  aspectRatio: 1,           // 1:1 比例
  viewMode: 1,              // 限制裁剪框在画布内
  dragMode: 'move',         // 拖拽模式
  autoCropArea: 0.8,        // 自动裁剪区域比例
  restore: false,           // 不恢复裁剪
  guides: false,            // 不显示网格线
  center: false,            // 不显示中心指示器
  highlight: false,         // 不高亮裁剪区域
  cropBoxMovable: true,     // 裁剪框可移动
  cropBoxResizable: true,   // 裁剪框可调整大小
  toggleDragModeOnDblclick: false, // 双击不切换拖拽模式
}
```

## 组件状态

组件有两种显示状态：

1. **未拍照状态**: 显示拍照按钮
2. **已拍照状态**: 显示图片预览，支持预览和删除

## 工作流程

1. 用户点击拍照按钮
2. 打开摄像头模态框
3. 用户拍照
4. 如果启用裁剪，显示裁剪界面
5. 用户确认后自动上传
6. 上传成功后关闭模态框，显示图片预览

## 浏览器兼容性

- Chrome 53+
- Firefox 36+
- Safari 11+
- Edge 12+

## 注意事项

1. 需要 HTTPS 环境或 localhost 才能访问摄像头
2. 用户需要授权摄像头权限
3. 组件会自动处理不同摄像头的兼容性
4. 裁剪功能依赖 cropperjs 库
5. 上传接口需要返回标准格式: `{ code: 200, data: { url: 'xxx' } }`

## 错误处理

组件内置了完善的错误处理机制：

- 摄像头权限被拒绝
- 浏览器不支持摄像头
- 网络上传失败
- 裁剪操作异常

所有错误都会通过 message 组件显示给用户。

## 与 MtcnUploadImgSingle 的区别

| 特性       | MtcnCameraCapture | MtcnUploadImgSingle |
| ---------- | ----------------- | ------------------- |
| 文件上传   | ❌                | ✅                  |
| 摄像头拍照 | ✅                | ❌                  |
| 图片裁剪   | ✅                | ❌                  |
| 自动上传   | ✅                | ✅                  |
| API 兼容性 | ✅                | ✅                  |

## 使用场景

- 现场拍照验证
- 身份证拍照
- 头像拍摄
- 证件照拍摄
- 现场取证
- 实时图片采集

完全替代需要拍照功能的场景，无需额外的事件处理代码。
