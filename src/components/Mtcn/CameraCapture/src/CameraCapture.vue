<template>
  <div class="camera-capture">
    <!-- 如果已有图片，显示MtcnUploadImgSingle组件 -->
    <MtcnUploadImgSingle v-if="imageUrl" v-model:value="imageUrl" :tipText="tipText" :type="type" @change="handleUploadChange" />

    <!-- 如果摄像头启用失败，显示本地上传组件 -->
    <MtcnUploadImgSingle
      v-else-if="cameraFailed"
      v-model:value="imageUrl"
      :tipText="tipText || '摄像头启用失败，请使用本地上传'"
      :type="type"
      @change="handleUploadChange" />

    <!-- 如果没有图片且摄像头正常，显示拍照按钮 -->
    <div v-else class="camera-button">
      <a-button type="primary" @click="openCamera" :loading="uploading" :disabled="disabled">
        <template #icon>
          <CameraOutlined />
        </template>
        {{ buttonText }}
      </a-button>
      <div class="ant-upload-text" v-if="tipText">{{ tipText }}</div>
    </div>

    <!-- 拍照模态框 -->
    <BasicModal v-model:open="cameraVisible" title="现场拍照" width="800px" :canFullscreen="false" :footer="null" :maskClosable="false" @cancel="closeCamera">
      <div class="camera-container">
        <!-- 隐藏的canvas，用于拍照 -->
        <canvas ref="canvasRef" style="display: none"></canvas>

        <!-- 摄像头预览 -->
        <div class="camera-preview" v-if="!capturedImage">
          <video ref="videoRef" autoplay playsinline muted :style="{ width: '100%', height: '400px', objectFit: 'cover' }"></video>
          <div class="camera-controls">
            <a-button type="primary" size="large" @click="capturePhoto">
              <template #icon>
                <CameraOutlined />
              </template>
              拍照
            </a-button>
            <a-button @click="closeCamera" size="large">关闭</a-button>
          </div>
        </div>

        <!-- 图片预览和裁剪 -->
        <div class="image-preview" v-else>
          <div class="preview-container">
            <div class="cropper-container" v-if="enableCrop">
              <CropperImage :src="capturedImage" height="400px" :options="computedCropperOptions" @cropend="handleCropEnd" @ready="handleCropperReady" />
            </div>
            <div v-else class="simple-preview">
              <img :src="capturedImage" alt="captured" style="max-width: 100%; max-height: 400px" />
            </div>
          </div>
          <div class="preview-controls">
            <a-space>
              <a-button @click="retakePhoto">重新拍照</a-button>
              <a-button v-if="enableCrop" type="primary" @click="confirmCrop" :disabled="!croppedData" :loading="uploading"> 确认上传 </a-button>
              <a-button v-else type="primary" @click="confirmPhoto" :loading="uploading"> 确认上传 </a-button>
            </a-space>
          </div>
        </div>
      </div>
    </BasicModal>
  </div>
</template>

<script lang="ts" setup>
  import { ref, nextTick, onUnmounted, watch, computed } from 'vue';
  import { CameraOutlined } from '@ant-design/icons-vue';
  import { useMessage } from '@/hooks/web/useMessage';
  import { useGlobSetting } from '@/hooks/setting';
  import { getToken } from '@/utils/auth';
  import { BasicModal } from '@/components/Modal';
  import { MtcnUploadImgSingle } from '@/components/Mtcn/Upload';
  import CropperImage from '@/components/Cropper/src/Cropper.vue';
  import { Form } from 'ant-design-vue';

  defineOptions({ name: 'MtcnCameraCapture', inheritAttrs: false });

  interface Props {
    value?: string;
    buttonText?: string;
    tipText?: string;
    enableCrop?: boolean;
    cropperOptions?: any;
    imageQuality?: number;
    maxWidth?: number;
    maxHeight?: number;
    type?: string;
    disabled?: boolean;
    cropWidth?: number;
    cropHeight?: number;
  }

  const props = withDefaults(defineProps<Props>(), {
    value: '',
    buttonText: '拍照',
    tipText: '',
    enableCrop: true,
    imageQuality: 0.8,
    maxWidth: 1920,
    maxHeight: 1080,
    type: 'annexpic',
    disabled: false,
    cropWidth: 0, // 0表示使用默认比例
    cropHeight: 0, // 0表示使用默认比例
    cropperOptions: () => ({
      aspectRatio: 1,
      viewMode: 1,
      dragMode: 'move',
      autoCropArea: 0.8,
      restore: false,
      guides: false,
      center: false,
      highlight: false,
      cropBoxMovable: true,
      cropBoxResizable: true,
      toggleDragModeOnDblclick: false,
    }),
  });

  const emit = defineEmits(['update:value', 'change']);

  const { createMessage } = useMessage();
  const globSetting = useGlobSetting();
  const formItemContext = Form.useInjectFormItemContext();

  const imageUrl = ref<string>('');
  const uploading = ref(false);
  const cameraVisible = ref(false);
  const cameraFailed = ref(false);
  const videoRef = ref<HTMLVideoElement>();
  const canvasRef = ref<HTMLCanvasElement>();
  const capturedImage = ref('');
  const croppedData = ref<any>(null);
  let mediaStream: MediaStream | null = null;

  // 计算裁剪器配置
  const computedCropperOptions = computed(() => {
    let aspectRatio = props.cropperOptions.aspectRatio;

    // 如果传入了具体的裁剪尺寸，计算宽高比
    if (props.cropWidth > 0 && props.cropHeight > 0) {
      aspectRatio = props.cropWidth / props.cropHeight;
    }

    return {
      ...props.cropperOptions,
      aspectRatio,
    };
  });

  // 监听value变化
  watch(
    () => props.value,
    val => {
      imageUrl.value = val;
      // 如果有图片了，重置摄像头失败状态
      if (val) {
        cameraFailed.value = false;
      }
    },
    { immediate: true },
  );

  // 打开摄像头
  const openCamera = async () => {
    try {
      await nextTick();
      console.log('开始启动摄像头');

      // 先检查浏览器支持，如果不支持直接抛出错误，避免显示摄像头界面
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        throw new Error('浏览器不支持摄像头功能');
      }

      cameraVisible.value = true;
      await startCamera();
      console.log('摄像头启动成功');
    } catch (error) {
      console.error('打开摄像头失败:', error);
      createMessage.error('无法访问摄像头，请使用本地上传');
      cameraVisible.value = false;
      cameraFailed.value = true;
    }
  };

  // 启动摄像头
  const startCamera = async () => {
    // 获取摄像头权限和流
    const constraints = {
      video: {
        width: { ideal: 1280 },
        height: { ideal: 720 },
        facingMode: 'user', // 前置摄像头
      },
      audio: false,
    };

    try {
      mediaStream = await navigator.mediaDevices.getUserMedia(constraints);
      if (videoRef.value) {
        videoRef.value.srcObject = mediaStream;
      }
    } catch (error) {
      // 如果前置摄像头失败，尝试后置摄像头
      try {
        const fallbackConstraints = {
          video: {
            width: { ideal: 1280 },
            height: { ideal: 720 },
            facingMode: 'environment',
          },
          audio: false,
        };
        mediaStream = await navigator.mediaDevices.getUserMedia(fallbackConstraints);
        if (videoRef.value) {
          videoRef.value.srcObject = mediaStream;
        }
      } catch (fallbackError) {
        // 最后尝试不指定摄像头方向
        const basicConstraints = {
          video: true,
          audio: false,
        };
        mediaStream = await navigator.mediaDevices.getUserMedia(basicConstraints);
        if (videoRef.value) {
          videoRef.value.srcObject = mediaStream;
        }
      }
    }
  };

  // 拍照
  const capturePhoto = () => {
    if (!videoRef.value || !canvasRef.value) {
      console.log('video或canvas元素不存在');
      createMessage.error('拍照失败：摄像头未准备好');
      return;
    }

    const video = videoRef.value;
    const canvas = canvasRef.value;
    const ctx = canvas.getContext('2d');

    if (!ctx) {
      console.log('无法获取canvas上下文');
      createMessage.error('拍照失败：无法获取画布上下文');
      return;
    }

    // 设置画布尺寸
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;

    // 绘制视频帧到画布
    ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

    // 转换为base64
    const imageData = canvas.toDataURL('image/jpeg', props.imageQuality);

    capturedImage.value = imageData;

    // 停止摄像头
    stopCamera();

    console.log('拍照完成');
  };

  // 重新拍照
  const retakePhoto = () => {
    capturedImage.value = '';
    croppedData.value = null;
    startCamera();
  };

  // 裁剪完成回调
  const handleCropEnd = (data: any) => {
    croppedData.value = data;
  };

  // 裁剪器准备就绪
  const handleCropperReady = () => {
    // 裁剪器准备就绪
  };

  // 确认裁剪并上传
  const confirmCrop = async () => {
    if (!croppedData.value) {
      createMessage.warning('请先进行裁剪操作');
      return;
    }

    const finalImage = croppedData.value.imgBase64;
    await uploadImage(finalImage);
  };

  // 确认照片并上传（不裁剪）
  const confirmPhoto = async () => {
    await uploadImage(capturedImage.value);
  };

  // 上传图片
  const uploadImage = async (imageData: string) => {
    uploading.value = true;
    try {
      // 将base64转换为File对象
      const file = await base64ToFile(imageData, 'camera-capture.jpg');

      // 创建FormData上传
      const formData = new FormData();
      formData.append('file', file);

      // 手动上传
      const response = await fetch(globSetting.uploadUrl + '/' + props.type, {
        method: 'POST',
        headers: { Authorization: getToken() as string },
        body: formData,
      });

      const result = await response.json();

      if (result.code === 200) {
        imageUrl.value = result.data.url;
        emit('update:value', imageUrl.value);
        emit('change', imageUrl.value);
        formItemContext.onFieldChange();
        createMessage.success('照片上传成功');
        closeCamera();
      } else {
        createMessage.error(result.msg || '上传失败');
      }
    } catch (error) {
      console.error('上传失败:', error);
      createMessage.error('上传失败，请重试');
    } finally {
      uploading.value = false;
    }
  };

  // base64转File
  const base64ToFile = (base64: string, filename: string): Promise<File> => {
    return new Promise(resolve => {
      const arr = base64.split(',');
      const mime = arr[0].match(/:(.*?);/)?.[1] || 'image/jpeg';
      const bstr = atob(arr[1]);
      let n = bstr.length;
      const u8arr = new Uint8Array(n);
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n);
      }
      resolve(new File([u8arr], filename, { type: mime }));
    });
  };

  // 停止摄像头
  const stopCamera = () => {
    if (mediaStream) {
      mediaStream.getTracks().forEach(track => track.stop());
      mediaStream = null;
    }
  };

  // 关闭摄像头
  const closeCamera = () => {
    stopCamera();
    cameraVisible.value = false;
    capturedImage.value = '';
    croppedData.value = null;
  };

  // 处理MtcnUploadImgSingle的变化
  const handleUploadChange = (url: string) => {
    imageUrl.value = url;
    emit('update:value', url);
    emit('change', url);
    formItemContext.onFieldChange();

    // 如果上传了图片，重置摄像头失败状态
    if (url) {
      cameraFailed.value = false;
    }
  };

  // 组件卸载时清理资源
  onUnmounted(() => {
    stopCamera();
  });
</script>

<style lang="less" scoped>
  .camera-capture {
    display: inline-block;
    position: relative;

    .camera-button {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      min-height: 115px;
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      background-color: #fafafa;
      transition: border-color 0.3s;
      padding: 10px;
      box-sizing: border-box;
      &:hover {
        border-color: #1890ff;
      }

      .ant-upload-text {
        margin-top: 8px;
        color: #666;
        font-size: 14px;
      }
    }
  }

  .camera-container {
    .camera-preview {
      text-align: center;

      .camera-controls {
        margin-top: 16px;
        display: flex;
        justify-content: center;
        gap: 12px;
        padding-bottom: 10px;
      }
    }

    .image-preview {
      .preview-container {
        text-align: center;
        margin-bottom: 16px;

        .cropper-container {
          border: 1px solid #d9d9d9;
          border-radius: 6px;
          overflow: hidden;
        }

        .simple-preview {
          display: flex;
          justify-content: center;
          align-items: center;
          border: 1px solid #d9d9d9;
          border-radius: 6px;
          min-height: 400px;
          background-color: #fafafa;
        }
      }

      .preview-controls {
        text-align: center;
        padding-bottom: 10px;
      }
    }
  }
</style>
