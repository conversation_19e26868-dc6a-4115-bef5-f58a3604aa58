<template>
  <div class="camera-upload">
    <!-- 如果已有图片，显示现有的上传组件 -->
    <MtcnUploadImgSingle
      v-if="!showCameraOnly"
      v-model:value="imageUrl"
      :tipText="tipText"
      :subTipText="subTipText"
      :type="type"
      :accept="accept"
      :disabled="disabled"
      :sizeUnit="sizeUnit"
      :fileSize="fileSize"
      @change="handleUploadChange" />

    <!-- 拍照区域 -->
    <div v-else class="camera-area">
      <div class="camera-section">
        <CameraCapture :buttonText="cameraButtonText" :enableCrop="enableCrop" :cropperOptions="cropperOptions" @captured="handleCameraCapture" />
        <div class="divider" v-if="enableUpload">或</div>
        <a-button v-if="enableUpload" @click="switchToUpload">
          {{ uploadTipText }}
        </a-button>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { computed, ref, watch } from 'vue';
  import { useGlobSetting } from '@/hooks/setting';
  import { useMessage } from '@/hooks/web/useMessage';
  import CameraCapture from './CameraCapture.vue';
  import { MtcnUploadImgSingle } from '@/components/Mtcn/Upload';

  defineOptions({ name: 'CameraUpload', inheritAttrs: false });

  interface Props {
    value?: string;
    tipText?: string;
    subTipText?: string;
    uploadTipText?: string;
    cameraButtonText?: string;
    type?: string;
    accept?: string;
    disabled?: boolean;
    sizeUnit?: string;
    fileSize?: number;
    enableCamera?: boolean;
    enableUpload?: boolean;
    enableCrop?: boolean;
    cropperOptions?: any;
  }

  const props = withDefaults(defineProps<Props>(), {
    value: '',
    tipText: '',
    subTipText: '',
    uploadTipText: '选择文件',
    cameraButtonText: '拍照',
    type: 'annexpic',
    accept: 'image/*',
    disabled: false,
    sizeUnit: 'MB',
    fileSize: 10,
    enableCamera: true,
    enableUpload: true,
    enableCrop: true,
    cropperOptions: () => ({
      aspectRatio: 1,
      viewMode: 1,
      dragMode: 'move',
      autoCropArea: 0.8,
    }),
  });

  const emit = defineEmits(['update:value', 'change']);

  const { createMessage } = useMessage();
  const globSetting = useGlobSetting();
  const imageUrl = ref<string>('');
  const showCameraOnly = ref<boolean>(false);

  watch(
    () => props.value,
    val => {
      imageUrl.value = val;
      // 如果没有图片且只启用拍照，显示拍照界面
      showCameraOnly.value = !val && props.enableCamera && !props.enableUpload;
    },
    { immediate: true },
  );

  // 拍照完成处理
  const handleCameraCapture = async (imageData: string) => {
    try {
      // 将base64转换为File对象
      const file = await base64ToFile(imageData, 'camera-capture.jpg');

      // 创建FormData上传
      const formData = new FormData();
      formData.append('file', file);

      // 手动上传
      const response = await fetch(globSetting.uploadUrl + '/' + props.type, {
        method: 'POST',
        headers: { Authorization: globSetting.apiUrl ? `Bearer ${globSetting.apiUrl}` : '' },
        body: formData,
      });

      const result = await response.json();

      if (result.code === 200) {
        imageUrl.value = result.data.url;
        emit('update:value', imageUrl.value);
        emit('change', imageUrl.value);
        createMessage.success('照片上传成功');
        showCameraOnly.value = false; // 上传成功后切换到显示模式
      } else {
        createMessage.error(result.msg || '上传失败');
      }
    } catch (error) {
      console.error('上传失败:', error);
      createMessage.error('上传失败，请重试');
    }
  };

  // 上传组件变化处理
  const handleUploadChange = (url: string) => {
    emit('update:value', url);
    emit('change', url);
  };

  // 切换到上传模式
  const switchToUpload = () => {
    showCameraOnly.value = false;
  };

  // base64转File
  const base64ToFile = (base64: string, filename: string): Promise<File> => {
    return new Promise(resolve => {
      const arr = base64.split(',');
      const mime = arr[0].match(/:(.*?);/)?.[1] || 'image/jpeg';
      const bstr = atob(arr[1]);
      let n = bstr.length;
      const u8arr = new Uint8Array(n);
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n);
      }
      resolve(new File([u8arr], filename, { type: mime }));
    });
  };
</script>

<style lang="less" scoped>
  .camera-upload {
    .camera-area {
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 120px;
      border: 2px dashed #d9d9d9;
      border-radius: 6px;
      background-color: #fafafa;
      transition: border-color 0.3s;

      &:hover {
        border-color: #1890ff;
      }

      .camera-section {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 12px;
      }

      .divider {
        color: #999;
        font-size: 14px;
        margin: 8px 0;
      }
    }
  }
</style>
