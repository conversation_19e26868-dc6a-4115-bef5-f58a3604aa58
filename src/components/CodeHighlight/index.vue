<template>
  <div class="CodeHighlight">
    <a-button size="small" @click="handleCopy(code)" class="manual-copy-btn">复制</a-button>
    <pre class="line-numbers">
      <code class="language-json" >
        {{ formatJson(code) }}
      </code>
    </pre>
  </div>
</template>

<script lang="ts" setup>
  import { onMounted, unref, nextTick } from 'vue';
  import Prism from 'prismjs';
  import 'prismjs/components/prism-markup';
  import 'prismjs/components/prism-json';
  import 'prismjs/themes/prism-okaidia.css';
  import { useMessage } from '@/hooks/web/useMessage';
  import { useCopyToClipboard } from '@/hooks/web/useCopyToClipboard';

  const { createMessage } = useMessage();
  defineProps({
    code: {
      type: String,
      default: '',
    },
  });

  const formatJson = (jsonStr: string) => {
    try {
      return JSON.stringify(JSON.parse(jsonStr), null, 2);
    } catch {
      return jsonStr;
    }
  };
  function handleCopy(text) {
    if (!text) return;
    const { isSuccessRef } = useCopyToClipboard(text);
    unref(isSuccessRef) && createMessage.success('复制成功');
  }
  onMounted(() => {
    nextTick(() => {
      Prism.highlightAll();
    });
  });
</script>
<style lang="less" scoped>
  .CodeHighlight {
    position: relative;
    .manual-copy-btn {
      position: absolute;
      top: 10px;
      right: 10px;
      z-index: 1;
    }
  }
</style>
