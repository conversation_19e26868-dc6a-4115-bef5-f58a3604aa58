<!--
 * @Description: 
 * @Autor: panmy
 * @Date: 2024-11-25 09:58:32
 * @LastEditors: Fhz
 * @LastEditTime: 2025-04-27 09:49:08
-->
<template>
  <div :class="getClass" :id="props.id == '' ? null : props.id" :name="props.id == '' ? null : `anchor_${props.id}`">
    <div :class="`${prefixCls}-content`" :style="{ 'justify-content': getContentPosition }">
      <slot v-if="slots.content"></slot>
      <div class="point" v-if="!slots.content && content">
        <BasicTitle :helpMessage="helpMessage">
          {{ content }}
          <template v-for="item in btnList">
            <a-button :size="item.size" :type="item.type" @click="handleClick(item)">{{ item.name }}</a-button>
          </template>
        </BasicTitle>
      </div>
    </div>
    <div :class="`${prefixCls}__action`" v-if="slots.action">
      <slot name="action" v-if="slots.action"></slot>
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { computed, useSlots } from 'vue';
  import BasicTitle from './BasicTitle.vue';
  import { useDesign } from '@/hooks/web/useDesign';

  const props = defineProps({
    id: {
      type: String,
      default: '',
    },
    helpMessage: {
      type: [String, Array] as PropType<string | string[]>,
      default: '',
    },
    content: {
      type: String,
      default: '',
    },
    contentPosition: {
      type: String as PropType<'left' | 'center' | 'right'>,
      default: 'left',
    },
    bordered: {
      type: Boolean,
      default: true,
    },
    btnList: {
      type: Array as PropType<any[]>,
      default: () => [],
    },
  });
  const slots = useSlots();
  const { prefixCls } = useDesign('basic-caption');
  const getClass = computed(() => [prefixCls, { [`${prefixCls}-border`]: props.bordered }]);
  const getContentPosition = computed(() => {
    if (props.contentPosition === 'left') return 'flex-start';
    if (props.contentPosition === 'right') return 'flex-end';
    return props.contentPosition;
  });
  const emit = defineEmits(['click']);
  function handleClick(item: any) {
    emit('click', item);
  }
</script>

<style lang="less" scoped>
  @prefix-cls: ~'@{namespace}-basic-caption';

  .@{prefix-cls} {
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-sizing: border-box;
    // &-border {
    //   border-bottom: 1px solid @border-color-base;
    // }
    &-content {
      flex: 1;
      display: flex;
    }
  }
  .point {
    display: flex;
    align-items: center;
    &::before {
      content: '';
      width: 4px;
      height: 15px;
      border-radius: 4px;
      background-color: @primary-color;
      display: block;
      margin-right: 8px;
      flex-shrink: 0;
    }
  }
</style>
